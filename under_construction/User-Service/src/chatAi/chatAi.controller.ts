import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Param,
  Req,
  UseGuards,
  Logger,
  ParseIntPipe,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiConsumes,
} from '@nestjs/swagger';
import { ChatAiService } from './chatAi.service';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
} from './dto/chatAi.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('ChatAI')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('chat-ai')
export class ChatAiController {
  private readonly logger = new Logger(ChatAiController.name);

  constructor(private readonly chatAiService: ChatAiService) {}

  // ==================== ChatAI Project Endpoints ====================

  @Post('project')
  @ApiOperation({ summary: 'Create ChatAI project for application' })
  @ApiResponse({
    status: 201,
    description: 'ChatAI project created successfully',
  })
  async createChatAiProject(
    @Body() createChatAiDto: CreateChatAiDto,
    @Query('appId') appId: string,
  ) {
    return this.chatAiService.createChatAiProject(createChatAiDto, appId);
  }

  @Get('project')
  @ApiOperation({ summary: 'Get ChatAI project by application ID' })
  @ApiResponse({
    status: 200,
    description: 'ChatAI project retrieved successfully',
  })
  async getChatAiProject(@Query('appId') appId: string) {
    return this.chatAiService.findChatAiProjectByAppId(appId);
  }

  @Patch('project/:id')
  @ApiOperation({ summary: 'Update ChatAI project' })
  @ApiResponse({
    status: 200,
    description: 'ChatAI project updated successfully',
  })
  async updateChatAiProject(
    @Param('id') id: string,
    @Body() updateChatAiDto: UpdateChatAiDto,
  ) {
    return this.chatAiService.updateChatAiProject(id, updateChatAiDto);
  }

  // ==================== Project Management ====================

  @Post('projects')
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({ status: 201, description: 'Project created successfully' })
  async createProject(
    @Body() createProjectDto: CreateProjectDto,
    @Query('chatAiId') chatAiId: string,
  ) {
    return this.chatAiService.createProject(createProjectDto, chatAiId);
  }

  @Get('projects')
  @ApiOperation({
    summary: 'Get user project (single project per application)',
  })
  @ApiResponse({ status: 200, description: 'Project retrieved successfully' })
  async getProject(
    @Query('userId') userId: string,
    @Query('chatAiId') chatAiId: string,
  ) {
    const project = await this.chatAiService.findProjectByUser(
      userId,
      chatAiId,
    );
    if (!project) {
      return {
        message: 'No project found for this application',
        project: null,
      };
    }
    return project;
  }

  @Get('projects/:id')
  @ApiOperation({ summary: 'Get project by ID' })
  @ApiResponse({ status: 200, description: 'Project retrieved successfully' })
  async getProject(
    @Param('id', ParseIntPipe) id: number,
    @Query('userId') userId: string,
  ) {
    return this.chatAiService.findProjectById(id, userId);
  }

  @Patch('projects/:id')
  @ApiOperation({ summary: 'Update project' })
  @ApiResponse({ status: 200, description: 'Project updated successfully' })
  async updateProject(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateProjectDto: UpdateProjectDto,
    @Query('userId') userId: string,
  ) {
    return this.chatAiService.updateProject(id, updateProjectDto, userId);
  }

  // ==================== Document Management ====================

  @Post('documents')
  @ApiOperation({ summary: 'Create document record (after file upload)' })
  @ApiResponse({ status: 201, description: 'Document created successfully' })
  async createDocument(
    @Body() createDocumentDto: CreateDocumentDto,
    @Query('projectId') projectId: string,
  ) {
    return this.chatAiService.createDocument(createDocumentDto, projectId);
  }

  @Post('documents/upload')
  @ApiOperation({ summary: 'Upload document file' })
  @ApiConsumes('multipart/form-data')
  @ApiResponse({ status: 201, description: 'Document uploaded successfully' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body('userId') userId: string,
    @Query('projectId') projectId: string,
  ) {
    // Create document record
    const createDocumentDto: CreateDocumentDto = {
      filename: file.originalname,
      filesize: file.size,
      contentType: file.mimetype,
      userId,
    };

    const document = await this.chatAiService.createDocument(
      createDocumentDto,
      projectId,
    );

    // TODO: Implement file processing pipeline
    // 1. Save file to storage (Supabase/S3)
    // 2. Parse with LlamaParse
    // 3. Index with LlamaIndex
    // 4. Update document status

    this.logger.log(`Document upload initiated: ${document.id}`);
    return {
      message: 'Document upload initiated',
      documentId: document.id,
      status: 'uploading',
    };
  }

  @Get('documents')
  @ApiOperation({ summary: 'Get documents by project' })
  @ApiResponse({ status: 200, description: 'Documents retrieved successfully' })
  async getDocuments(
    @Query('projectId', ParseIntPipe) projectId: number,
    @Query('userId') userId: string,
  ) {
    return this.chatAiService.findDocumentsByProject(projectId, userId);
  }

  @Patch('documents/:id')
  @ApiOperation({
    summary: 'Update document (internal use for processing pipeline)',
  })
  @ApiResponse({ status: 200, description: 'Document updated successfully' })
  async updateDocument(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDocumentDto: UpdateDocumentDto,
    @Query('userId') userId: string,
  ) {
    return this.chatAiService.updateDocument(id, updateDocumentDto, userId);
  }

  // ==================== Chat Management ====================

  @Post('chat')
  @ApiOperation({ summary: 'Send chat message and get AI response' })
  @ApiResponse({
    status: 201,
    description: 'Chat response generated successfully',
  })
  async chat(
    @Body() chatQueryDto: ChatQueryDto,
    @Query('chatAiId') chatAiId: string,
  ) {
    // TODO: Implement chat pipeline
    // 1. Validate project and user access
    // 2. Query LlamaIndex vector database
    // 3. Generate response with OpenRouter
    // 4. Save chat message
    // 5. Return streaming response

    this.logger.log(
      `Chat query received for project: ${chatQueryDto.projectId}`,
    );

    // Placeholder response
    const mockResponse = {
      question: chatQueryDto.question,
      response:
        'This is a placeholder response. Chat pipeline implementation pending.',
      sourceReferences: [],
      timestamp: new Date(),
    };

    // Save chat message
    const createChatMessageDto: CreateChatMessageDto = {
      question: chatQueryDto.question,
      response: mockResponse.response,
      projectId: chatQueryDto.projectId,
      documentId: chatQueryDto.documentId,
      sourceReferences: mockResponse.sourceReferences,
    };

    const savedMessage = await this.chatAiService.createChatMessage(
      createChatMessageDto,
      chatAiId,
    );

    return {
      ...mockResponse,
      messageId: savedMessage.id,
    };
  }

  @Get('chat/history')
  @ApiOperation({ summary: 'Get chat history for project' })
  @ApiResponse({
    status: 200,
    description: 'Chat history retrieved successfully',
  })
  async getChatHistory(
    @Query('projectId', ParseIntPipe) projectId: number,
    @Query('userId') userId: string,
    @Query('limit', ParseIntPipe) limit: number = 50,
  ) {
    return this.chatAiService.findChatHistory(projectId, userId, limit);
  }

  // ==================== Credit Management ====================

  @Get('credits/usage')
  @ApiOperation({ summary: 'Get credit usage history' })
  @ApiResponse({
    status: 200,
    description: 'Credit usage retrieved successfully',
  })
  async getCreditUsage(
    @Query('chatAiId') chatAiId: string,
    @Query('userId') userId: string,
  ) {
    return this.chatAiService.getCreditUsage(chatAiId, userId);
  }

  // ==================== Health Check ====================

  @Get('health')
  @ApiOperation({ summary: 'Health check for ChatAI service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date(),
      service: 'ChatAI',
      version: '1.0.0',
    };
  }
}
