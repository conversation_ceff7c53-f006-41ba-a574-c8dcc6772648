import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  Logger,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ChatAiService } from './chatAi.service';
import {
  CreateChatAiDto,
  UpdateChatAiDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto,
  FetchSingleChatAiDto,
  FetchChatAisDto,
  RemoveChatAiDto,
  UpdateChatAiSettingDto,
  GetChatAiTransactionDto,
} from './dto/chatAi.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';

@ApiTags('ChatAI')
@Controller('users/app/chatai')
export class ChatAiController {
  private readonly logger = new Logger(ChatAiController.name);

  constructor(private readonly chatAiService: ChatAiService) {}

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-chatai')
  async setupChatAi(
    @Req() req: { user: User },
    @Body() createChatAiDto: CreateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.setupChatAi(userId, createChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-chatai')
  async updateChatAi(
    @Req() req: { user: User },
    @Body() updateChatAiDto: UpdateChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAi(userId, updateChatAiDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-settings')
  async updateChatAiSetting(
    @Req() req: { user: User },
    @Body() updateChatAiSettingDto: UpdateChatAiSettingDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateChatAiSettings(
      userId,
      updateChatAiSettingDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-chatai')
  async getSingleChatAi(
    @Req() req: { user: User },
    @Query() query: FetchSingleChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getSingleChatAi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-chatais')
  async getAllChatAis(
    @Req() req: { user: User },
    @Query() query: FetchChatAisDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getAllChatAis(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-transactions')
  async getTransactions(
    @Query() query: GetChatAiTransactionDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getAllTransactions(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('remove-chatai')
  async removeChatAi(
    @Req() req: { user: User },
    @Body() removeChatAiDto: RemoveChatAiDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.removeChatAi(userId, removeChatAiDto);
  }

  // ==================== Document Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('upload-document')
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() createDocumentDto: CreateDocumentDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.uploadDocument(
      userId,
      file,
      createDocumentDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-documents')
  async getDocuments(
    @Query() query: FetchSingleChatAiDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getDocuments(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-document')
  async updateDocument(
    @Req() req: { user: User },
    @Body() updateDocumentDto: UpdateDocumentDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.updateDocument(userId, updateDocumentDto);
  }

  // ==================== Chat Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('chat')
  async chat(
    @Req() req: { user: User },
    @Body() chatQueryDto: ChatQueryDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.chat(userId, chatQueryDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-chat-history')
  async getChatHistory(
    @Query() query: FetchSingleChatAiDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getChatHistory(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('create-message')
  async createMessage(
    @Req() req: { user: User },
    @Body() createChatMessageDto: CreateChatMessageDto,
  ) {
    const userId = req.user.id;
    return await this.chatAiService.createMessage(userId, createChatMessageDto);
  }

  // ==================== Credit Management ====================

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-credit-usage')
  async getCreditUsage(
    @Query() query: FetchSingleChatAiDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.chatAiService.getCreditUsage(userId, query);
  }
}
