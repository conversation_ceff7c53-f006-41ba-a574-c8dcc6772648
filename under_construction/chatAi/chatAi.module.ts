import { Module, forwardRef } from "@nestjs/common";
import { ChatAiService } from "./chatAi.service";
import { ChatAiController } from "./chatAi.controller";
import { AuthModule } from "../auth/auth.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "../user/entities/user.entity";
import { GatewayModule } from "../gateway/gateway.module";
import { TransactionModule } from "../transaction/transaction.module";
import { ConfigModule } from "@nestjs/config";
import { Application } from "../application/entities/application.entity";
import { OriginsModule } from "../origins/origins.module";
import { PodCoordinatorModule } from "../pod-coordinator/pod-coordinator.module";
import { ClientsModule, Transport } from "@nestjs/microservices";

// ChatAI Entities
import { ChatAi } from "./entities/chatAi.entity";
import { ChatAiProject } from "./entities/project.entity";
import { ChatAiDocument } from "./entities/document.entity";
import { ChatAiMessage } from "./entities/message.entity";
import { ChatAiCreditUsage } from "./entities/credit-usage.entity";
import { ChatAiApiTransaction } from "./entities/transaction.entity";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([
      Application,
      User,
      // ChatAI Entities
      ChatAi,
      ChatAiProject,
      ChatAiDocument,
      ChatAiMessage,
      ChatAiCreditUsage,
      ChatAiApiTransaction,
    ]),
    // Remove RabbitMQ for now - will add later if needed
    // ClientsModule.register([...]),
    GatewayModule,
    TransactionModule,
    OriginsModule,
    PodCoordinatorModule,
  ],
  providers: [ChatAiService],
  controllers: [ChatAiController],
  exports: [ChatAiService],
})
export class ChatAiModule {}
