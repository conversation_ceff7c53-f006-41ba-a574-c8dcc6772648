import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';
import { OriginType } from '../../origins/dto/origins.dto';

export enum TranctionType {
  DEPOSIT = 'deposit',
  WITHDRAW = 'withdraw',
  ALL = 'all',
}

export class CreateRelayerDto {
  @ApiProperty({
    description: 'Name of the relayer',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Relayer name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name: string;

  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class FetchSingleRelayerDto {
  @ApiProperty({
    description: 'Id of app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class FetchRelayersDto {
  @ApiProperty({
    description: 'Page number',
    required: false,
    default: 1,
  })
  page?: number;

  @ApiProperty({
    description: 'Limit per page',
    required: false,
    default: 10,
  })
  limit?: number;

  @ApiProperty({
    description: 'Search keyword',
    required: false,
  })
  s?: string;
}

export class UpdateRelayerDto {
  @ApiPropertyOptional({
    description: 'ID of the app',
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Name of the Relayer',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Relayer name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  relayerName: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if the application is active',
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;
}

export class UpdateRelayerSettingDto {
  @ApiProperty({
    description: 'ID of the app',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Relayer Name',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  relayerName?: string;

  @ApiProperty({
    description: 'Balance of the relayer',
    required: false,
  })
  @IsOptional()
  @IsNumber(
    { maxDecimalPlaces: 18 },
    { message: 'Balance must be a valid decimal number' },
  )
  @Transform(({ value }) => value)
  balance?: number;

  @ApiProperty({
    description: 'Blocked balance of the relayer',
    required: false,
  })
  @IsOptional()
  @IsNumber(
    { maxDecimalPlaces: 18 },
    { message: 'Blocked balance must be a valid decimal number' },
  )
  @Transform(({ value }) => value)
  blockedBalance?: number;

  @ApiProperty({
    description: 'Threshold of min balance for notification',
    required: false,
  })
  @IsOptional()
  @IsNumber(
    { maxDecimalPlaces: 18 },
    { message: 'Threshold must be a valid decimal number' },
  )
  @Transform(({ value }) => parseFloat(value))
  threshold?: number;

  @ApiProperty({
    description: 'Is the relayer active?',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isActive?: boolean;

  @ApiProperty({
    description: 'Enable or disable notifications for the relayer',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  notificationsEnabled?: boolean;
}

export class AppTransactionDto extends FetchSingleRelayerDto {
  @ApiProperty({
    description: 'Transaction hash of app',
    required: true,
  })
  @IsNotEmpty()
  hash: string;

  @ApiProperty({
    description: 'Transaction type for your application',
    enum: [TranctionType.DEPOSIT, TranctionType.WITHDRAW],
    default: TranctionType.DEPOSIT,
  })
  @IsNotEmpty()
  @IsEnum(TranctionType)
  type: string;
}

export class GetAppTransactionDto extends FetchRelayersDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Transaction type for your application',
    enum: [TranctionType.DEPOSIT, TranctionType.WITHDRAW, TranctionType.ALL],
    default: TranctionType.ALL,
  })
  @IsNotEmpty()
  @IsEnum(TranctionType)
  type: string;
}

export class GetAppRelayerTransactionDto extends FetchRelayersDto {
  @ApiProperty({
    description: 'Id of your app relayer',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appRelayerId: string;
}
export class CreateAppConfigDto {
  @ApiProperty({
    description: 'Origin for the AppConfig',
    example: 'https://example.com',
  })
  @IsNotEmpty()
  @Matches(Regexs.Url, {
    message: 'Invalid URL format (ex: https://example.com)',
  })
  origin: string;

  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}
export class GetContractAbiDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Smart contract address',
    required: true,
  })
  @IsNotEmpty()
  contractAddress: string;
}

export class CreateSmartContractDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'Name of smart contract',
    minLength: 3,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Smart contract name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name: string;

  @ApiProperty({
    description: 'Address of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  address: string;

  @ApiProperty({
    description: 'ABI of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsString()
  abi: string;

  @ApiProperty({
    description: 'Methods of smart contract',
    required: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  methods: string[];

  @ApiProperty({
    description: 'Whitelisted methods of smart contract',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  whitelistedMethods?: string[];
}

export class AppIdDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class ListSmartContractsDto extends FetchRelayersDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;
}

export class UpdateSmartContractDto {
  @ApiProperty({
    description: `Id of your application`,
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: `Id of your application's smart contract`,
    required: true,
  })
  @IsNotEmpty()
  contractId: number;

  @ApiProperty({
    description: 'Name of smart contract',
    minLength: 3,
    maxLength: 50,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  @Matches(Regexs.OnlyAlphaNumericWithSpace, {
    message: 'Smart contract name must contain only Alphanumeric characters',
  })
  @Transform(({ value }) => value.replace(/\s+/g, ' ').trim()) // Trim whitespace before validation
  name?: string;

  @ApiProperty({
    description: 'Whitelisted methods of smart contract',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  whitelistedMethods?: string[];

  @ApiPropertyOptional({
    description: `Flag indicating if the application's samrt contract is active`,
  })
  @IsOptional()
  @IsBoolean()
  @IsNotEmpty()
  isActive?: boolean;
}

export class RemoveSmartContractDto {
  @ApiProperty({
    description: `Id of your application`,
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @ApiProperty({
    description: `Id of your application's smart contract`,
    required: true,
  })
  @IsNotEmpty()
  contractId: number;
}

export class RemoveRelayerDto {
  @ApiProperty({
    description: 'Id of your application',
    required: true,
  })
  @IsNotEmpty()
  @IsUUID()
  appId: string;

  @ApiProperty({
    description: 'origin configuration type',
    enum: Object.values(OriginType),
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(OriginType, { message: 'Invalid configuration type' })
  type: OriginType;
}
