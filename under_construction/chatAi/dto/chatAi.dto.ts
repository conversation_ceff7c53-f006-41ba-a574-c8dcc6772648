import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Not<PERSON>mpty,
  IsO<PERSON>al,
  Is<PERSON><PERSON>ber,
  IsBoolean,
  IsEmail,
  IsIn,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

// ChatAI Main DTOs
export class CreateChatAiDto {
  @ApiProperty({
    description: "Name of the ChatAI service",
    example: "My Document AI Assistant",
  })
  @IsString()
  @IsNotEmpty()
  chatAiName: string;

  @ApiPropertyOptional({
    description: "LlamaCloud API key for document parsing",
  })
  @IsOptional()
  @IsString()
  llamaCloudApiKey?: string;

  @ApiPropertyOptional({
    description: "OpenRouter API key for AI responses",
  })
  @IsOptional()
  @IsString()
  openRouterApiKey?: string;

  @ApiPropertyOptional({
    description: "Initial credits for the service",
    default: 30,
  })
  @IsOptional()
  @IsNumber()
  credits?: number;

  @ApiPropertyOptional({
    description: "Subscription status",
    enum: ["free", "pro"],
    default: "free",
  })
  @IsOptional()
  @IsString()
  @IsIn(["free", "pro"])
  subscriptionStatus?: string;

  @ApiPropertyOptional({
    description: "Enable notifications",
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  notificationsEnabled?: boolean;

  @ApiPropertyOptional({
    description: "Email for notifications",
  })
  @IsOptional()
  @IsEmail()
  notificationEmail?: string;
}

export class UpdateChatAiDto {
  @ApiPropertyOptional({
    description: "Name of the ChatAI service",
  })
  @IsOptional()
  @IsString()
  chatAiName?: string;

  @ApiPropertyOptional({
    description: "LlamaCloud API key for document parsing",
  })
  @IsOptional()
  @IsString()
  llamaCloudApiKey?: string;

  @ApiPropertyOptional({
    description: "OpenRouter API key for AI responses",
  })
  @IsOptional()
  @IsString()
  openRouterApiKey?: string;

  @ApiPropertyOptional({
    description: "Credits for the service",
  })
  @IsOptional()
  @IsNumber()
  credits?: number;

  @ApiPropertyOptional({
    description: "Subscription status",
    enum: ["free", "pro"],
  })
  @IsOptional()
  @IsString()
  @IsIn(["free", "pro"])
  subscriptionStatus?: string;

  @ApiPropertyOptional({
    description: "Enable notifications",
  })
  @IsOptional()
  @IsBoolean()
  notificationsEnabled?: boolean;

  @ApiPropertyOptional({
    description: "Email for notifications",
  })
  @IsOptional()
  @IsEmail()
  notificationEmail?: string;

  @ApiPropertyOptional({
    description: "Active status",
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

// Project DTOs
export class CreateProjectDto {
  @ApiProperty({
    description: "Project name",
    example: "Company Documents",
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: "Project description",
    example: "All company policy documents and manuals",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: "Supabase user ID",
    example: "123e4567-e89b-12d3-a456-426614174000",
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class UpdateProjectDto {
  @ApiPropertyOptional({
    description: "Project name",
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: "Project description",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: "LlamaIndex vector database ID",
  })
  @IsOptional()
  @IsString()
  indexId?: string;

  @ApiPropertyOptional({
    description: "Project status",
    enum: ["active", "archived", "deleted"],
  })
  @IsOptional()
  @IsString()
  @IsIn(["active", "archived", "deleted"])
  status?: string;
}

// Document DTOs
export class CreateDocumentDto {
  @ApiProperty({
    description: "Document filename",
    example: "company-policy.pdf",
  })
  @IsString()
  @IsNotEmpty()
  filename: string;

  @ApiProperty({
    description: "File size in bytes",
    example: 1024000,
  })
  @IsNumber()
  filesize: number;

  @ApiProperty({
    description: "MIME content type",
    example: "application/pdf",
  })
  @IsString()
  @IsNotEmpty()
  contentType: string;

  @ApiProperty({
    description: "Project ID",
    example: 1,
  })
  @IsNumber()
  projectId: number;

  @ApiProperty({
    description: "Supabase user ID",
    example: "123e4567-e89b-12d3-a456-426614174000",
  })
  @IsString()
  @IsNotEmpty()
  userId: string;
}

export class UpdateDocumentDto {
  @ApiPropertyOptional({
    description: "Document processing status",
    enum: ["uploading", "parsing", "indexing", "ready", "error"],
  })
  @IsOptional()
  @IsString()
  @IsIn(["uploading", "parsing", "indexing", "ready", "error"])
  status?: string;

  @ApiPropertyOptional({
    description: "Parsed document data",
  })
  @IsOptional()
  parsedData?: any;

  @ApiPropertyOptional({
    description: "AI-generated document summary",
  })
  @IsOptional()
  summary?: any;

  @ApiPropertyOptional({
    description: "Number of pages in document",
  })
  @IsOptional()
  @IsNumber()
  pageCount?: number;

  @ApiPropertyOptional({
    description: "Word count in document",
  })
  @IsOptional()
  @IsNumber()
  wordCount?: number;

  @ApiPropertyOptional({
    description: "Error message if processing failed",
  })
  @IsOptional()
  @IsString()
  errorMessage?: string;
}

// Chat DTOs
export class CreateChatMessageDto {
  @ApiProperty({
    description: "User question",
    example: "What is the company vacation policy?",
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: "AI response",
    example: "According to the company policy document...",
  })
  @IsString()
  @IsNotEmpty()
  response: string;

  @ApiProperty({
    description: "Project ID",
    example: 1,
  })
  @IsNumber()
  projectId: number;

  @ApiPropertyOptional({
    description: "Specific document ID (optional)",
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  documentId?: number;

  @ApiPropertyOptional({
    description: "Source references from vector search",
  })
  @IsOptional()
  sourceReferences?: any;

  @ApiPropertyOptional({
    description: "Admin debug session flag",
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isAdminDebug?: boolean;
}

export class ChatQueryDto {
  @ApiProperty({
    description: "User question",
    example: "What is the company vacation policy?",
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: "Project ID",
    example: 1,
  })
  @IsNumber()
  projectId: number;

  @ApiPropertyOptional({
    description: "Specific document ID (optional)",
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  documentId?: number;
}
