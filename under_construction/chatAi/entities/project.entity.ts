import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { ChatAi } from './chatAi.entity';
import { ChatAiDocument } from './document.entity';
import { ChatAiMessage } from './message.entity';

@Entity('chat_ai_projects')
export class ChatAiProject {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: false })
  userId: string; // Supabase user ID

  @Column({ nullable: true })
  indexId: string; // LlamaIndex vector DB embedding ID for the entire project

  @Column({ 
    nullable: false, 
    default: 'active' 
  })
  status: string; // active, archived, deleted

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => ChatAi, (chatAi) => chatAi.projects, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'chatAiId' })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;

  @OneToMany(() => ChatAiDocument, (document) => document.project)
  documents: ChatAiDocument[];

  @OneToMany(() => ChatAiMessage, (message) => message.project)
  messages: ChatAiMessage[];
}
