import { Application } from "src/application/entities/application.entity";
import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  OneToMany,
} from "typeorm";
import { ChatAiProject } from "./project.entity";
import { ChatAiDocument } from "./document.entity";
import { ChatAiMessage } from "./message.entity";
import { ChatAiCreditUsage } from "./credit-usage.entity";

@Entity("chat_ai")
export class ChatAi {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false })
  chatAiName: string;

  // AI Service Configuration
  @Column({ nullable: true })
  llamaCloudApiKey: string;

  @Column({ nullable: true })
  openRouterApiKey: string;

  // Credit System
  @Column({
    default: 30,
    type: "integer",
  })
  credits: number;

  @Column({
    default: 0,
    type: "integer",
  })
  totalCreditsUsed: number;

  @Column({
    nullable: false,
    default: "free",
  })
  subscriptionStatus: string; // free, pro

  @Column({
    type: "timestamp",
    nullable: true,
  })
  subscriptionStartDate: Date;

  @Column({
    type: "timestamp",
    nullable: true,
  })
  subscriptionEndDate: Date;

  // Settings
  @Column({ nullable: false, default: true })
  isActive: boolean;

  @Column({ nullable: false, default: false })
  notificationsEnabled: boolean;

  @Column({ nullable: true })
  notificationEmail: string;

  @CreateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp",
    default: () => "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  // Relationships
  @OneToOne(() => Application, (app) => app.chatAi, {
    cascade: ["insert", "update", "remove"],
    onDelete: "CASCADE",
  })
  @JoinColumn()
  app: Application;

  @OneToOne(() => ChatAiProject, (project) => project.chatAi)
  project: ChatAiProject;

  @OneToMany(() => ChatAiDocument, (document) => document.chatAi)
  documents: ChatAiDocument[];

  @OneToMany(() => ChatAiMessage, (message) => message.chatAi)
  messages: ChatAiMessage[];

  @OneToMany(() => ChatAiCreditUsage, (usage) => usage.chatAi)
  creditUsage: ChatAiCreditUsage[];
}
