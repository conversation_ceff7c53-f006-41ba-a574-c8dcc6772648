import { Application } from 'src/application/entities/application.entity';
import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';

@Entity()
export class ChatAi {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  chatAiName: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 18,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return value;
      },
    },
  })
  tokens: string;

  // @Column({
  //   default: 0,
  //   type: 'decimal',
  //   precision: 30,
  //   scale: 18,
  //   transformer: {
  //     to(value) {
  //       return value;
  //     },
  //     from(value: string) {
  //       return value?.toString();
  //     },
  //   },
  // })
  // blockedBalance: string;

  // @Column({
  //   default: 0,
  //   type: 'decimal',
  //   precision: 30,
  //   scale: 18,
  //   transformer: {
  //     to(value) {
  //       return value;
  //     },
  //     from(value: string) {
  //       return value?.toString();
  //     },
  //   },
  // })
  // threshold: string;

  @Column({ nullable: false, default: true })
  isActive: boolean;

  // @Column({ nullable: false, default: false })
  // notificationsEnabled: boolean;

  // @Column({ nullable: true })
  // notificationEmail: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  // @Column({ type: 'timestamp', nullable: true })
  // lastNotifiedAt: Date;

  @OneToOne(() => Application, (app) => app.chatAi, {
    cascade: ['insert', 'update', 'remove'],
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  app: Application;
}
