import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatAi } from './entities/chatAi.entity';
import { ChatAiProject } from './entities/project.entity';
import { ChatAiDocument } from './entities/document.entity';
import { ChatAiMessage } from './entities/message.entity';
import { ChatAiCreditUsage } from './entities/credit-usage.entity';
import { ChatAiApiTransaction } from './entities/transaction.entity';
import { 
  CreateChatAiDto, 
  UpdateChatAiDto,
  CreateProjectDto,
  UpdateProjectDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateChatMessageDto,
  ChatQueryDto
} from './dto/chatAi.dto';
import { Application } from '../application/entities/application.entity';

@Injectable()
export class ChatAiService {
  private readonly logger = new Logger(ChatAiService.name);

  constructor(
    @InjectRepository(ChatAi)
    private readonly chatAiRepository: Repository<ChatAi>,
    
    @InjectRepository(ChatAiProject)
    private readonly projectRepository: Repository<ChatAiProject>,
    
    @InjectRepository(ChatAiDocument)
    private readonly documentRepository: Repository<ChatAiDocument>,
    
    @InjectRepository(ChatAiMessage)
    private readonly messageRepository: Repository<ChatAiMessage>,
    
    @InjectRepository(ChatAiCreditUsage)
    private readonly creditUsageRepository: Repository<ChatAiCreditUsage>,
    
    @InjectRepository(ChatAiApiTransaction)
    private readonly apiTransactionRepository: Repository<ChatAiApiTransaction>,
    
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
  ) {}

  // ==================== ChatAI Main Service Methods ====================

  async createChatAi(createChatAiDto: CreateChatAiDto, appId: string): Promise<ChatAi> {
    try {
      // Find the application
      const application = await this.applicationRepository.findOne({
        where: { id: appId }
      });

      if (!application) {
        throw new NotFoundException('Application not found');
      }

      // Create ChatAI instance
      const chatAi = this.chatAiRepository.create({
        ...createChatAiDto,
        app: application,
      });

      const savedChatAi = await this.chatAiRepository.save(chatAi);
      this.logger.log(`ChatAI created successfully: ${savedChatAi.id}`);
      
      return savedChatAi;
    } catch (error) {
      this.logger.error(`Failed to create ChatAI: ${error.message}`);
      throw error;
    }
  }

  async findChatAiByAppId(appId: string): Promise<ChatAi> {
    const chatAi = await this.chatAiRepository.findOne({
      where: { app: { id: appId } },
      relations: ['app', 'projects', 'documents', 'messages'],
    });

    if (!chatAi) {
      throw new NotFoundException('ChatAI service not found for this application');
    }

    return chatAi;
  }

  async updateChatAi(id: string, updateChatAiDto: UpdateChatAiDto): Promise<ChatAi> {
    const chatAi = await this.chatAiRepository.findOne({ where: { id } });
    
    if (!chatAi) {
      throw new NotFoundException('ChatAI service not found');
    }

    Object.assign(chatAi, updateChatAiDto);
    const updatedChatAi = await this.chatAiRepository.save(chatAi);
    
    this.logger.log(`ChatAI updated successfully: ${id}`);
    return updatedChatAi;
  }

  // ==================== Project Management ====================

  async createProject(createProjectDto: CreateProjectDto, chatAiId: string): Promise<ChatAiProject> {
    try {
      // Check if ChatAI exists
      const chatAi = await this.chatAiRepository.findOne({ where: { id: chatAiId } });
      if (!chatAi) {
        throw new NotFoundException('ChatAI service not found');
      }

      // Check credit limits
      await this.checkAndDeductCredits(chatAiId, createProjectDto.userId, 'project_create', 1);

      // Create project
      const project = this.projectRepository.create({
        ...createProjectDto,
        chatAi,
        chatAiId,
      });

      const savedProject = await this.projectRepository.save(project);
      this.logger.log(`Project created successfully: ${savedProject.id}`);
      
      return savedProject;
    } catch (error) {
      this.logger.error(`Failed to create project: ${error.message}`);
      throw error;
    }
  }

  async findProjectsByUser(userId: string, chatAiId: string): Promise<ChatAiProject[]> {
    return this.projectRepository.find({
      where: { 
        userId, 
        chatAiId,
        status: 'active' 
      },
      relations: ['documents'],
      order: { createdAt: 'DESC' },
    });
  }

  async findProjectById(id: number, userId: string): Promise<ChatAiProject> {
    const project = await this.projectRepository.findOne({
      where: { id, userId },
      relations: ['documents', 'messages'],
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    return project;
  }

  async updateProject(id: number, updateProjectDto: UpdateProjectDto, userId: string): Promise<ChatAiProject> {
    const project = await this.findProjectById(id, userId);
    
    Object.assign(project, updateProjectDto);
    const updatedProject = await this.projectRepository.save(project);
    
    this.logger.log(`Project updated successfully: ${id}`);
    return updatedProject;
  }

  // ==================== Document Management ====================

  async createDocument(createDocumentDto: CreateDocumentDto, chatAiId: string): Promise<ChatAiDocument> {
    try {
      // Check if project exists and belongs to user
      const project = await this.findProjectById(createDocumentDto.projectId, createDocumentDto.userId);
      
      // Check credit limits
      await this.checkAndDeductCredits(chatAiId, createDocumentDto.userId, 'document_upload', 1);

      // Create document
      const document = this.documentRepository.create({
        ...createDocumentDto,
        chatAiId,
        project,
      });

      const savedDocument = await this.documentRepository.save(document);
      this.logger.log(`Document created successfully: ${savedDocument.id}`);
      
      return savedDocument;
    } catch (error) {
      this.logger.error(`Failed to create document: ${error.message}`);
      throw error;
    }
  }

  async findDocumentsByProject(projectId: number, userId: string): Promise<ChatAiDocument[]> {
    // Verify project ownership
    await this.findProjectById(projectId, userId);
    
    return this.documentRepository.find({
      where: { projectId },
      order: { createdAt: 'DESC' },
    });
  }

  async updateDocument(id: number, updateDocumentDto: UpdateDocumentDto, userId: string): Promise<ChatAiDocument> {
    const document = await this.documentRepository.findOne({
      where: { id, userId },
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    Object.assign(document, updateDocumentDto);
    const updatedDocument = await this.documentRepository.save(document);
    
    this.logger.log(`Document updated successfully: ${id}`);
    return updatedDocument;
  }

  // ==================== Chat Management ====================

  async createChatMessage(createChatMessageDto: CreateChatMessageDto, chatAiId: string): Promise<ChatAiMessage> {
    try {
      // Verify project ownership
      const project = await this.projectRepository.findOne({
        where: { 
          id: createChatMessageDto.projectId,
          chatAiId 
        },
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }

      // Create message
      const message = this.messageRepository.create({
        ...createChatMessageDto,
        chatAiId,
        project,
      });

      const savedMessage = await this.messageRepository.save(message);
      this.logger.log(`Chat message created successfully: ${savedMessage.id}`);
      
      return savedMessage;
    } catch (error) {
      this.logger.error(`Failed to create chat message: ${error.message}`);
      throw error;
    }
  }

  async findChatHistory(projectId: number, userId: string, limit: number = 50): Promise<ChatAiMessage[]> {
    // Verify project ownership
    await this.findProjectById(projectId, userId);
    
    return this.messageRepository.find({
      where: { projectId },
      order: { timestamp: 'DESC' },
      take: limit,
    });
  }

  // ==================== Credit Management ====================

  async checkAndDeductCredits(
    chatAiId: string, 
    userId: string, 
    actionType: string, 
    creditsRequired: number
  ): Promise<void> {
    const chatAi = await this.chatAiRepository.findOne({ where: { id: chatAiId } });
    
    if (!chatAi) {
      throw new NotFoundException('ChatAI service not found');
    }

    // Check if user has enough credits
    if (chatAi.credits < creditsRequired) {
      throw new BadRequestException('Insufficient credits');
    }

    // Deduct credits
    chatAi.credits -= creditsRequired;
    chatAi.totalCreditsUsed += creditsRequired;
    await this.chatAiRepository.save(chatAi);

    // Log credit usage
    const creditUsage = this.creditUsageRepository.create({
      chatAiId,
      userId,
      actionType,
      creditsUsed: creditsRequired,
    });

    await this.creditUsageRepository.save(creditUsage);
    this.logger.log(`Credits deducted: ${creditsRequired} for action: ${actionType}`);
  }

  async getCreditUsage(chatAiId: string, userId: string): Promise<ChatAiCreditUsage[]> {
    return this.creditUsageRepository.find({
      where: { chatAiId, userId },
      order: { timestamp: 'DESC' },
      take: 100,
    });
  }

  // ==================== API Transaction Logging ====================

  async logApiTransaction(
    chatAiId: string,
    apiProvider: string,
    endpoint: string,
    requestType: string,
    requestData: any,
    responseData: any,
    status: string,
    tokensUsed?: number,
    cost?: number,
    errorMessage?: string
  ): Promise<ChatAiApiTransaction> {
    const transaction = this.apiTransactionRepository.create({
      chatAiId,
      apiProvider,
      endpoint,
      requestType,
      requestData,
      responseData,
      status,
      tokensUsed,
      cost,
      errorMessage,
    });

    return this.apiTransactionRepository.save(transaction);
  }
}
